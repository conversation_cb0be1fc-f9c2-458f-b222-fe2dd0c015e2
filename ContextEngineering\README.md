# 🏗️ ContextEngineering - Mémoire Technique du Projet

## 📋 Vue d'ensemble
Ce dossier sert de **mémoire secondaire technique** pour l'application TimeTracker V4. Il contient toute la documentation, l'architecture, les notes de développement et l'historique des modifications.

## 📁 Structure du dossier

### `/Architecture/`
- **`system-overview.md`** : Vue d'ensemble du système complet
- **`components-map.md`** : Cartographie des composants React
- **`data-flow.md`** : Flux de données et interactions
- **`dependencies.md`** : Gestion des dépendances et versions

### `/TechnicalNotes/`
- **`gsap-animations.md`** : Documentation des animations GSAP
- **`solar-calculations.md`** : Logique des calculs solaires et astronomiques
- **`performance-optimizations.md`** : Optimisations de performance
- **`troubleshooting.md`** : Guide de résolution des problèmes courants

### `/ChangeHistory/`
- **`conflicts-resolution.md`** : Historique des conflits résolus
- **`feature-evolution.md`** : Évolution des fonctionnalités
- **`bug-fixes.md`** : Historique des corrections de bugs

### `/DevNotes/`
- **`current-issues.md`** : Problèmes actuels identifiés
- **`future-improvements.md`** : Améliorations futures planifiées
- **`testing-scenarios.md`** : Scénarios de test

## 🎯 Objectif
Maintenir une trace complète de :
- ✅ Toutes les décisions techniques prises
- ✅ Les problèmes rencontrés et leurs solutions
- ✅ L'évolution de l'architecture
- ✅ Les optimisations appliquées
- ✅ Les conflits résolus

## 📝 Utilisation
Ce dossier est consulté à chaque session de développement pour :
1. **Comprendre** l'état actuel du projet
2. **Éviter** de reproduire les mêmes erreurs
3. **Maintenir** la cohérence technique
4. **Documenter** les nouvelles modifications

---
*Créé le 2025-01-22 - Système de mémoire technique pour TimeTracker V4*
